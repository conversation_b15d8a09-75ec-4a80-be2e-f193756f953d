<script lang="ts">
  import { onMount } from 'svelte';
  import { Button } from '$lib/components/ui/button';
  import Input from '$lib/components/ui/input.svelte';
  import Label from '$lib/components/ui/label.svelte';
  import type { ProjectWithDetails, ResearchDrug, ResearchDrugWithDetails, DrugGroup } from '$lib/services/projectManagementService';
  import { sqliteDictionaryService } from '$lib/services/sqliteDictionaryService';

  // 组件属性
  const props = $props<{projectDetails: ProjectWithDetails}>();
  let projectDetailsData = $derived(props.projectDetails);
  
  console.log('ProjectDrugs组件接收到的项目详情:', projectDetailsData);

  // 状态管理
  let researchDrugs = $state<ResearchDrugWithDetails[]>([]);
  let drugGroups = $state<DrugGroup[]>([]);
  let drugDialogOpen = $state(false);

  // 编辑状态
  let editingDrugId = $state<number | null>(null);
  let isEditMode = $state(false);

  // 新药物和编辑药物的详细信息
  let newDrug = $state('');
  let newDrugType = $state<number | null>(null);
  let newDrugClassification = $state<number | null>(null);
  let newUsageMethod = $state<number | null>(null);
  let newUsageFrequency = $state<number | null>(null);
  let newMechanismOfAction = $state<number | null>(null);
  let newDosage = $state('');
  let newShare = $state<number | null>(null);
  let newDrugCharacteristics = $state('');
  let newNotes = $state('');

  // 字典数据
  let drugTypes = $state<{item_id: number, item_value: string}[]>([]);
  let drugClassifications = $state<{item_id: number, item_value: string}[]>([]);
  let usageMethods = $state<{item_id: number, item_value: string}[]>([]);
  let usageFrequencies = $state<{item_id: number, item_value: string}[]>([]);
  let mechanismsOfAction = $state<{item_id: number, item_value: string}[]>([]);

  // localStorage 键名
  const STORAGE_KEY_DRUGS = 'new_project_research_drugs';
  const STORAGE_KEY_GROUPS = 'new_project_drug_groups';

  // 从 localStorage 加载数据
  function loadFromLocalStorage() {
    if (!projectDetailsData || !projectDetailsData.project) {
      console.error('项目详情不完整，无法加载研究药物数据');
      return;
    }
    
    if (projectDetailsData.project.project_id) {
      // 如果项目已有ID，使用项目详情中的数据
      researchDrugs = projectDetailsData.research_drugs || [];
      drugGroups = projectDetailsData.drug_groups || [];
      return;
    }

    try {
      // 尝试从 localStorage 加载数据
      const storedDrugs = localStorage.getItem(STORAGE_KEY_DRUGS);
      const storedGroups = localStorage.getItem(STORAGE_KEY_GROUPS);

      if (storedDrugs) {
        const parsedDrugs = JSON.parse(storedDrugs);
        researchDrugs = parsedDrugs;
        projectDetailsData.research_drugs = parsedDrugs;
      } else {
        researchDrugs = [];
      }

      // 注意：药物分组功能已合并到研究药物中，但为了向后兼容保留
      if (storedGroups) {
        const parsedGroups = JSON.parse(storedGroups);
        drugGroups = parsedGroups;
        projectDetailsData.drug_groups = parsedGroups;
      } else {
        drugGroups = [];
      }

      console.log('从 localStorage 加载数据:', { researchDrugs, drugGroups });
    } catch (error) {
      console.error('从 localStorage 加载数据失败:', error);
      researchDrugs = projectDetailsData.research_drugs || [];
      drugGroups = projectDetailsData.drug_groups || [];
    }
  }

  // 处理数据同步
  $effect(() => {
    if (projectDetailsData?.research_drugs) {
      researchDrugs = projectDetailsData.research_drugs;
    }
    
    if (projectDetailsData?.drug_groups) {
      drugGroups = projectDetailsData.drug_groups;
    }
  });

  // 保存数据到 localStorage
  function saveToLocalStorage() {
    if (!projectDetailsData || !projectDetailsData.project) {
      console.error('项目详情不完整，无法保存研究药物数据');
      return;
    }
    
    if (projectDetailsData.project.project_id) {
      // 如果项目已有ID，不需要保存到 localStorage
      return;
    }

    try {
      localStorage.setItem(STORAGE_KEY_DRUGS, JSON.stringify(researchDrugs));
      localStorage.setItem(STORAGE_KEY_GROUPS, JSON.stringify(drugGroups));
      console.log('保存数据到 localStorage 成功');
    } catch (error) {
      console.error('保存数据到 localStorage 失败:', error);
    }
  }

  // 清除 localStorage 中的数据
  export function clearLocalStorage() {
    localStorage.removeItem(STORAGE_KEY_DRUGS);
    localStorage.removeItem(STORAGE_KEY_GROUPS);
    console.log('清除 localStorage 数据成功');
  }

  // 加载字典数据
  async function loadDictionaryData() {
    try {
      // 加载药物类型字典
      const drugTypeDict = await sqliteDictionaryService.getDictByName('药物类型');
      if (drugTypeDict && drugTypeDict.items) {
        drugTypes = drugTypeDict.items.map(item => ({
          item_id: item.item_id || 0,
          item_value: item.value
        }));
      }

      // 加载药物分类字典
      const drugClassDict = await sqliteDictionaryService.getDictByName('药物分类');
      if (drugClassDict && drugClassDict.items) {
        drugClassifications = drugClassDict.items.map(item => ({
          item_id: item.item_id || 0,
          item_value: item.value
        }));
      }

      // 加载用药方法字典
      const usageMethodDict = await sqliteDictionaryService.getDictByName('用药方法');
      if (usageMethodDict && usageMethodDict.items) {
        usageMethods = usageMethodDict.items.map(item => ({
          item_id: item.item_id || 0,
          item_value: item.value
        }));
      }

      // 加载用药频率字典
      const usageFreqDict = await sqliteDictionaryService.getDictByName('用药频率');
      if (usageFreqDict && usageFreqDict.items) {
        usageFrequencies = usageFreqDict.items.map(item => ({
          item_id: item.item_id || 0,
          item_value: item.value
        }));
      }

      // 加载药物作用机制字典
      const mechanismDict = await sqliteDictionaryService.getDictByName('药物作用机制');
      if (mechanismDict && mechanismDict.items) {
        mechanismsOfAction = mechanismDict.items.map(item => ({
          item_id: item.item_id || 0,
          item_value: item.value
        }));
      }

      // 给药途径字典已删除，与用药方法重复

      console.log('字典数据加载完成:', {
        drugTypes,
        drugClassifications,
        usageMethods,
        usageFrequencies,
        mechanismsOfAction
      });
    } catch (error) {
      console.error('加载字典数据失败:', error);
    }
  }

  // 组件挂载时加载数据
  onMount(async () => {
    await loadDictionaryData();
    loadFromLocalStorage();
  });

  // 添加或更新研究药物
  function addOrUpdateResearchDrug() {
    if (!newDrug || !projectDetailsData || !projectDetailsData.project) return;

    console.log('添加/更新研究药物:', {
      drug: newDrug,
      drugType: newDrugType,
      classification: newDrugClassification,
      method: newUsageMethod,
      frequency: newUsageFrequency,
      mechanismOfAction: newMechanismOfAction,
      dosage: newDosage,
      share: newShare,
      characteristics: newDrugCharacteristics,
      notes: newNotes,
      isEditMode: isEditMode
    });

    // 创建药物对象
    const drug: ResearchDrugWithDetails = {
      drug_info_id: isEditMode ? (editingDrugId ?? undefined) : undefined,
      project_id: projectDetailsData.project.project_id || '', 
      research_drug: newDrug,
      drug_type_item_id: newDrugType ?? undefined,
      drug_classification_item_id: newDrugClassification ?? undefined,
      usage_method_item_id: newUsageMethod ?? undefined,
      usage_frequency_item_id: newUsageFrequency ?? undefined,
      mechanism_of_action_item_id: newMechanismOfAction ?? undefined,
      dosage: newDosage || undefined,
      share: newShare ?? undefined,
      drug_characteristics: newDrugCharacteristics || undefined,
      notes: newNotes || undefined,
      // 添加字典项详情
      drug_type: newDrugType ? {
        item_id: drugTypes.find(t => t.item_id === newDrugType)?.item_id || 0,
        dictionary_id: 0,
        item_key: '',
        item_value: drugTypes.find(t => t.item_id === newDrugType)?.item_value || ''
      } : undefined,
      drug_classification: newDrugClassification ? {
        item_id: drugClassifications.find(d => d.item_id === newDrugClassification)?.item_id || 0,
        dictionary_id: 0,
        item_key: '',
        item_value: drugClassifications.find(d => d.item_id === newDrugClassification)?.item_value || ''
      } : undefined,
      usage_method: newUsageMethod ? {
        item_id: usageMethods.find(m => m.item_id === newUsageMethod)?.item_id || 0,
        dictionary_id: 0,
        item_key: '',
        item_value: usageMethods.find(m => m.item_id === newUsageMethod)?.item_value || ''
      } : undefined,
      usage_frequency: newUsageFrequency ? {
        item_id: usageFrequencies.find(f => f.item_id === newUsageFrequency)?.item_id || 0,
        dictionary_id: 0,
        item_key: '',
        item_value: usageFrequencies.find(f => f.item_id === newUsageFrequency)?.item_value || ''
      } : undefined,
      mechanism_of_action: newMechanismOfAction ? {
        item_id: mechanismsOfAction.find(m => m.item_id === newMechanismOfAction)?.item_id || 0,
        dictionary_id: 0,
        item_key: '',
        item_value: mechanismsOfAction.find(m => m.item_id === newMechanismOfAction)?.item_value || ''
      } : undefined,
    };

    if (isEditMode && editingDrugId !== null) {
      // 编辑模式：更新现有药物
      const index = researchDrugs.findIndex(d => d.drug_info_id === editingDrugId);
      if (index !== -1) {
        researchDrugs[index] = drug;
        researchDrugs = [...researchDrugs]; // 触发响应式更新
        
        if (projectDetailsData.research_drugs) {
          projectDetailsData.research_drugs[index] = drug;
        }
      }
    } else {
      // 添加模式：添加新药物
      researchDrugs = [...researchDrugs, drug];
      
      if (Array.isArray(projectDetailsData.research_drugs)) {
        projectDetailsData.research_drugs.push(drug);
      } else {
        projectDetailsData.research_drugs = researchDrugs;
      }
    }

    // 保存到 localStorage
    saveToLocalStorage();

    console.log('更新后的研究药物列表:', researchDrugs);

    // 重置表单并关闭对话框
    resetDrugForm();
    drugDialogOpen = false;
  }

  // 重置药物表单
  function resetDrugForm() {
    newDrug = '';
    newDrugType = null;
    newDrugClassification = null;
    newUsageMethod = null;
    newUsageFrequency = null;
    newMechanismOfAction = null;
    newDosage = '';
    newShare = null;
    newDrugCharacteristics = '';
    newNotes = '';
    editingDrugId = null;
    isEditMode = false;
  }

  // 编辑研究药物
  function editResearchDrug(drug: ResearchDrugWithDetails) {
    // 设置编辑模式
    isEditMode = true;
    editingDrugId = drug.drug_info_id || null;
    
    // 填充表单数据
    newDrug = drug.research_drug;
    newDrugType = drug.drug_type_item_id || null;
    newDrugClassification = drug.drug_classification_item_id || null;
    newUsageMethod = drug.usage_method_item_id || null;
    newUsageFrequency = drug.usage_frequency_item_id || null;
    newMechanismOfAction = drug.mechanism_of_action_item_id || null;
    newDosage = drug.dosage || '';
    newShare = drug.share || null;
    newDrugCharacteristics = drug.drug_characteristics || '';
    newNotes = drug.notes || '';
    
    // 打开对话框
    drugDialogOpen = true;
  }

  // 删除研究药物
  function removeResearchDrug(index: number) {
    if (!researchDrugs || researchDrugs.length === 0) return;

    console.log('删除研究药物:', { index, drug: researchDrugs[index] });

    // 更新本地状态
    researchDrugs = researchDrugs.filter((_: any, i: number) => i !== index);

    // 更新 projectDetails
    if (projectDetailsData.research_drugs) {
      projectDetailsData.research_drugs = projectDetailsData.research_drugs.filter((_: any, i: number) => i !== index);
    } else {
      projectDetailsData.research_drugs = researchDrugs;
    }

    // 保存到 localStorage
    saveToLocalStorage();

    console.log('删除后的研究药物列表:', researchDrugs);
  }

  // 注意：药物分组功能已合并到研究药物表中的share字段
  // 相关的 addDrugGroup 和 removeDrugGroup 函数已移除
</script>

<div class="space-y-10">
  <!-- 研究药物 -->
  <div>
    <div class="flex justify-between items-center mb-6">
      <div>
        <h2 class="text-xl font-semibold">研究药物</h2>
        <p class="text-sm text-gray-500 mt-1">添加项目中使用的研究药物</p>
      </div>

      <Button on:click={() => { resetDrugForm(); drugDialogOpen = true; }} variant="outline" class="gap-2">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
        添加研究药物
      </Button>

      {#if drugDialogOpen}
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div class="bg-white p-6 rounded-lg shadow-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <h3 class="text-lg font-semibold mb-4">{isEditMode ? '编辑研究药物' : '添加研究药物'}</h3>
            <p class="text-gray-500 text-sm mb-4">请填写研究药物的详细信息</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 py-4">
              <!-- 药物名称 -->
              <div class="md:col-span-2">
                <Label forAttr="research_drug">药物名称 *</Label>
                <Input
                  id="research_drug"
                  bind:value={newDrug}
                  placeholder="请输入药物名称"
                />
              </div>

              <!-- 药物类型 -->
              <div>
                <Label forAttr="drug_type">药物类型 *</Label>
                <select
                  id="drug_type"
                  bind:value={newDrugType}
                  class="h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                >
                  <option value={null}>请选择药物类型</option>
                  {#each drugTypes as drugType}
                    <option value={drugType.item_id}>{drugType.item_value}</option>
                  {/each}
                </select>
                <p class="text-xs text-gray-500 mt-1">研究药物、对照药物或安慰剂</p>
              </div>

              <!-- 份额/占比 -->
              <div>
                <Label forAttr="share">份额/占比</Label>
                <Input
                  id="share"
                  type="number"
                  min="0"
                  step="0.01"
                  bind:value={newShare}
                  placeholder="如：0.5或 50"
                />
                <p class="text-xs text-gray-500 mt-1">表示该药物在项目中的占比</p>
              </div>

              <!-- 药物分类 -->
              <div>
                <Label forAttr="drug_classification">药物分类</Label>
                <select
                  id="drug_classification"
                  bind:value={newDrugClassification}
                  class="h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                >
                  <option value={null}>请选择药物分类</option>
                  {#each drugClassifications as classification}
                    <option value={classification.item_id}>{classification.item_value}</option>
                  {/each}
                </select>
              </div>

              <!-- 用药方法 -->
              <div>
                <Label forAttr="usage_method">用药方法</Label>
                <select
                  id="usage_method"
                  bind:value={newUsageMethod}
                  class="h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                >
                  <option value={null}>请选择用药方法</option>
                  {#each usageMethods as method}
                    <option value={method.item_id}>{method.item_value}</option>
                  {/each}
                </select>
              </div>

              <!-- 用药频率 -->
              <div>
                <Label forAttr="usage_frequency">用药频率</Label>
                <select
                  id="usage_frequency"
                  bind:value={newUsageFrequency}
                  class="h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                >
                  <option value={null}>请选择用药频率</option>
                  {#each usageFrequencies as frequency}
                    <option value={frequency.item_id}>{frequency.item_value}</option>
                  {/each}
                </select>
              </div>

              <!-- 药物作用机制 -->
              <div>
                <Label forAttr="mechanism_of_action">药物作用机制</Label>
                <select
                  id="mechanism_of_action"
                  bind:value={newMechanismOfAction}
                  class="h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                >
                  <option value={null}>请选择药物作用机制</option>
                  {#each mechanismsOfAction as mechanism}
                    <option value={mechanism.item_id}>{mechanism.item_value}</option>
                  {/each}
                </select>
              </div>

              <!-- 剂量信息 -->
              <div>
                <Label forAttr="dosage">剂量信息</Label>
                <Input
                  id="dosage"
                  bind:value={newDosage}
                  placeholder="如：100mg"
                />
              </div>

              <!-- 药物特性描述 -->
              <div class="md:col-span-2">
                <Label forAttr="drug_characteristics">药物特性描述</Label>
                <textarea
                  id="drug_characteristics"
                  bind:value={newDrugCharacteristics}
                  placeholder="描述药物的特性、作用机制等..."
                  class="w-full min-h-[80px] rounded-md border border-input bg-background px-3 py-2 text-sm resize-y"
                ></textarea>
              </div>

              <!-- 其他备注 -->
              <div class="md:col-span-2">
                <Label forAttr="notes">其他备注</Label>
                <textarea
                  id="notes"
                  bind:value={newNotes}
                  placeholder="其他备注信息..."
                  class="w-full min-h-[60px] rounded-md border border-input bg-background px-3 py-2 text-sm resize-y"
                ></textarea>
              </div>
            </div>

            <div class="flex justify-end gap-2 pt-4 border-t">
              <Button on:click={() => { drugDialogOpen = false; resetDrugForm(); }} variant="outline">取消</Button>
              <Button on:click={addOrUpdateResearchDrug} disabled={!newDrug || newDrugType === null}>
                {isEditMode ? '保存修改' : '添加药物'}
              </Button>
            </div>
          </div>
        </div>
      {/if}
    </div>

    {#if !researchDrugs || researchDrugs.length === 0}
      <div class="bg-gray-50 border border-gray-200 rounded-lg p-8 text-center">
        <div class="flex justify-center mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="text-gray-400"><circle cx="12" cy="12" r="10"></circle><path d="M12 8v8"></path><path d="M8 12h8"></path></svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无研究药物数据</h3>
        <p class="text-gray-500 mb-4">点击"添加研究药物"按钮来添加项目的研究药物</p>
        <Button on:click={() => { resetDrugForm(); drugDialogOpen = true; }} variant="outline" class="gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
          添加研究药物
        </Button>
      </div>
    {:else}
      <div class="bg-white border rounded-lg overflow-hidden">
        <div class="overflow-x-auto">
          <table class="w-full border-collapse min-w-[900px]">
            <thead>
              <tr class="bg-gray-50">
                <th class="text-left py-3 px-4 font-medium text-gray-700">药物名称</th>
                <th class="text-left py-3 px-4 font-medium text-gray-700">类型</th>
                <th class="text-left py-3 px-4 font-medium text-gray-700">分类</th>
                <th class="text-left py-3 px-4 font-medium text-gray-700">用药方法</th>
                <th class="text-left py-3 px-4 font-medium text-gray-700">频率</th>
                <th class="text-left py-3 px-4 font-medium text-gray-700">作用机制</th>
                <th class="text-left py-3 px-4 font-medium text-gray-700">剂量</th>
                <th class="text-left py-3 px-4 font-medium text-gray-700">份额</th>
                <th class="w-[120px] text-right py-3 px-4 font-medium text-gray-700">操作</th>
              </tr>
            </thead>
            <tbody>
              {#each researchDrugs as drug, index}
                <tr class="border-t hover:bg-gray-50">
                  <td class="py-3 px-4 font-medium">{drug.research_drug}</td>
                  <td class="py-3 px-4">
                    {#if drug.drug_type}
                      <span class="inline-block bg-indigo-50 text-indigo-700 px-2 py-1 rounded-md text-xs font-medium">
                        {drug.drug_type.item_value}
                      </span>
                    {:else}
                      <span class="text-gray-400 text-sm">-</span>
                    {/if}
                  </td>
                  <td class="py-3 px-4">
                    {#if drug.drug_classification}
                      <span class="inline-block bg-blue-50 text-blue-700 px-2 py-1 rounded-md text-xs">
                        {drug.drug_classification.item_value}
                      </span>
                    {:else}
                      <span class="text-gray-400 text-sm">-</span>
                    {/if}
                  </td>
                  <td class="py-3 px-4">
                    {#if drug.usage_method}
                      <span class="inline-block bg-green-50 text-green-700 px-2 py-1 rounded-md text-xs">
                        {drug.usage_method.item_value}
                      </span>
                    {:else}
                      <span class="text-gray-400 text-sm">-</span>
                    {/if}
                  </td>
                  <td class="py-3 px-4">
                    {#if drug.usage_frequency}
                      <span class="inline-block bg-purple-50 text-purple-700 px-2 py-1 rounded-md text-xs">
                        {drug.usage_frequency.item_value}
                      </span>
                    {:else}
                      <span class="text-gray-400 text-sm">-</span>
                    {/if}
                  </td>
                  <td class="py-3 px-4">
                    {#if drug.mechanism_of_action}
                      <span class="inline-block bg-orange-50 text-orange-700 px-2 py-1 rounded-md text-xs">
                        {drug.mechanism_of_action.item_value}
                      </span>
                    {:else}
                      <span class="text-gray-400 text-sm">-</span>
                    {/if}
                  </td>
                  <td class="py-3 px-4">
                    {drug.dosage || '-'}
                  </td>
                  <td class="py-3 px-4">
                    {drug.share ? drug.share.toString() : '-'}
                  </td>
                  <td class="text-right py-3 px-4">
                    <div class="flex gap-1 justify-end">
                      <Button variant="ghost" size="sm" on:click={() => editResearchDrug(drug)} class="text-blue-500 hover:text-blue-700">
                        编辑
                      </Button>
                      <Button variant="ghost" size="sm" on:click={() => removeResearchDrug(index)} class="text-red-500 hover:text-red-700">
                        删除
                      </Button>
                    </div>
                  </td>
                </tr>
                <!-- 如果有备注，显示在下一行 -->
                <!-- 如果有药物特性或备注，显示在下一行 -->
                {#if drug.drug_characteristics || drug.notes}
                  <tr class="border-t-0">
                    <td colspan="9" class="py-2 px-4 bg-gray-50 text-sm text-gray-600 space-y-1">
                      {#if drug.drug_characteristics}
                        <div>
                          <span class="font-medium">特性：</span>{drug.drug_characteristics}
                        </div>
                      {/if}
                      {#if drug.notes}
                        <div>
                          <span class="font-medium">备注：</span>{drug.notes}
                        </div>
                      {/if}
                    </td>
                  </tr>
                {/if}
              {/each}
            </tbody>
          </table>
        </div>
      </div>
    {/if}
  </div>

  <!-- 药物分组信息提示 -->
  <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
    <div class="flex items-start gap-3">
      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-600 mt-0.5 flex-shrink-0"><circle cx="12" cy="12" r="10"></circle><path d="M12 16v-4"></path><path d="M12 8h.01"></path></svg>
      <div>
        <h3 class="text-sm font-medium text-blue-800 mb-1">药物分组功能已合并</h3>
        <p class="text-sm text-blue-700">
          药物分组功能已合并到上方的研究药物表中。您可以在添加或编辑研究药物时直接设置“份额/占比”字段。
        </p>
      </div>
    </div>
  </div>

  <!-- 帮助提示 -->
  <div class="mt-6 p-4 bg-green-50 rounded-lg border border-green-200">
    <h3 class="text-sm font-medium text-green-800 mb-2">研究药物信息说明</h3>
    <ul class="list-disc pl-5 text-sm text-green-700 space-y-1">
      <li><strong>药物类型</strong>：必填项，用于区分研究药物、对照药物或安慰剂</li>
      <li><strong>份额/占比</strong>：表示该药物在项目中的占比或使用量</li>
      <li><strong>药物特性</strong>：描述药物的特性、作用机制等信息</li>
      <li><strong>编辑功能</strong>：可以对已添加的研究药物进行修改</li>
      <li>所有研究药物信息为可选项，您可以根据需要填写</li>
    </ul>
  </div>
</div>

